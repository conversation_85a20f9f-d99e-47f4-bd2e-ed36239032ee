body {
    --nav-width: 200px;
    margin: 0 0 0 var(--nav-width);
    font-family: 'Quicksand', sans-serif;
    font-size: 18px;
}

.nav {
    position: fixed;
    top: 0;
    left: 0;
    width: var(--nav-width);
    height: 100vh;
    background: #222222
}

.nav__link {
    display: block;
    padding: 12px 18px;
    text-decoration: none;
    color: #eeeeee;
    font-weight: 500;
}

.nav__link:hover {
    background: #333333;
}

#app {
    margin: 2em;
    line-height: 1.5;
    font-weight: 500;
}

a {
    color: #009578
}

h1 {
    font-size: 2.5rem;
    margin-bottom: 20px;
}

p {
    font-size: 1.2rem;
}

.title {
    color: darkblue;
    margin-bottom: 10px;
}

.btn {
    padding: 10px;
    margin-right: 8px;
    background-color: #4c93af;
    color: white;
    cursor: pointer;
}

form input {
    margin: 5px 0;
    padding: 8px;
    width: 200px;
    display: block;
}

table {
    font-family: arial, sans-serif;
    margin: 20px 0;
}

td,
th {
    border: 1px solid #dddddd;
    text-align: left;
    padding: 8px;
}

tr:nth-child(even) {
    background-color: #dddddd;
}